{"total_tests": 9, "successful_tests": 0, "failed_tests": 9, "test_details": [{"test_name": "Basic Search", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.04037618637084961}, {"test_name": "Query Expansion (HyDE)", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010706186294555664}, {"test_name": "Multi-Step Reasoning", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010587930679321289}, {"test_name": "Full RAG Features", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010970115661621094}, {"test_name": "Low Relevance Threshold", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010141134262084961}, {"test_name": "Output Format: text", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010123968124389648}, {"test_name": "Output Format: json", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011279106140136719}, {"test_name": "Output Format: markdown", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011816263198852539}, {"test_name": "Output Format: table", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011939764022827148}], "rag_techniques_verified": {"hybrid_search": false, "query_expansion": false, "multi_step_reasoning": false, "context_aware": false, "citation_engine": false, "router_engine": false}, "performance_metrics": {"total_time": 0.12794065475463867, "average_time": 0.014215628306070963, "min_time": 0.010123968124389648, "max_time": 0.04037618637084961}}