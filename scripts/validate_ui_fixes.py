#!/usr/bin/env python3
"""
Validate UI fixes without browser automation.
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)

# Change to the project directory
os.chdir(project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult

def validate_ui_fixes():
    """Validate all UI fixes."""
    print("🔍 VALIDATING UI FIXES")
    print("=" * 50)
    
    # Create a test client
    client = Client()
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'username': '<EMAIL>', 'first_name': 'Mahesh'}
    )
    
    # Login the user
    client.force_login(user)
    print(f"✅ User logged in: {user.email}")
    
    # Test query
    query = "List issues reported by <PERSON>"
    print(f"\n🔍 Testing query: '{query}'")
    
    # Make the search request
    response = client.post('/search/query/', {
        'query': query,
        'use_hybrid_search': True,
        'use_context_aware': True,
        'use_query_expansion': False,
        'use_multi_step_reasoning': False
    })
    
    if response.status_code == 200:
        print("✅ Search request successful")
        
        # Get the latest search result
        latest_result = SearchResult.objects.latest('timestamp')
        
        # Check response formatting
        content = response.content.decode('utf-8')
        
        print(f"\n📊 Search Result Analysis:")
        print(f"   ID: {latest_result.id}")
        print(f"   Citations: {latest_result.citations.count()}")
        print(f"   Raw answer length: {len(latest_result.generated_answer)}")
        
        # Validation checks
        checks = [
            ("Response container found", 'professional-response-container' in content),
            ("Response content div found", 'response-content' in content),
            ("Citations section found", 'professional-sources-section' in content),
            ("Source cards found", 'professional-source-card' in content),
            ("Has proper list structure", '<ul class="response-list professional-list">' in content),
            ("Has list items", '<li class="response-list-item">' in content),
            ("Has proper heading", '<h2 class="response-heading response-h2">' in content),
            ("Citations count > 0", latest_result.citations.count() > 0),
            ("Response not empty", len(latest_result.generated_answer.strip()) > 0),
            ("Contains bullet points", '•' in latest_result.generated_answer or '-' in content)
        ]
        
        print(f"\n📋 Validation Results:")
        success_count = 0
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if result:
                success_count += 1
        
        total_checks = len(checks)
        success_rate = success_count / total_checks * 100
        
        print(f"\n📊 Overall Score: {success_count}/{total_checks} ({success_rate:.1f}%)")
        
        if success_count == total_checks:
            print("🎉 ALL CHECKS PASSED! UI fixes are working perfectly!")
        elif success_count >= total_checks * 0.8:
            print("✅ MOSTLY WORKING! Minor issues may remain.")
        else:
            print("⚠️  NEEDS ATTENTION! Some fixes may not be working.")
        
        # Show a sample of the formatted HTML
        if '<h2 class="response-heading response-h2">' in content:
            print(f"\n📄 Sample HTML Output:")
            start = content.find('<h2 class="response-heading response-h2">')
            end = content.find('</ul>', start) + 5
            if start != -1 and end != -1:
                sample = content[start:end]
                print("   " + sample.replace('\n', '\n   ')[:500] + "...")
        
        return success_rate >= 80
        
    else:
        print(f"❌ Search request failed with status: {response.status_code}")
        return False

if __name__ == "__main__":
    success = validate_ui_fixes()
    if success:
        print("\n🚀 UI FIXES VALIDATION: SUCCESS!")
    else:
        print("\n❌ UI FIXES VALIDATION: FAILED!")
    sys.exit(0 if success else 1)
