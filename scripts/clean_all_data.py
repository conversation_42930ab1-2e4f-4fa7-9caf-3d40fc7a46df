#!/usr/bin/env python3
"""
Clean all data from both Django database and Qdrant vector database.
This will remove all documents, chunks, embeddings, and search results.
"""

import os
import sys
import django
import requests
from typing import List

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.conf import settings
from apps.documents.models import RawDocument, DocumentChunk, DocumentSource, EmbeddingMetadata
from apps.search.models import SearchResult, SearchQuery, ResultCitation
from apps.accounts.models import Tenant

def clean_qdrant_collections():
    """Clean all Qdrant collections."""
    print("🗑️  CLEANING QDRANT VECTOR DATABASE")
    print("-" * 40)
    
    qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
    qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
    qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
    
    try:
        # Get all collections
        response = requests.get(f"{qdrant_url}/collections")
        if response.status_code == 200:
            collections = response.json()
            collection_names = [col['name'] for col in collections.get('result', {}).get('collections', [])]
            
            print(f"Found {len(collection_names)} collections:")
            for name in collection_names:
                print(f"   - {name}")
            
            # Delete each collection
            deleted_count = 0
            for collection_name in collection_names:
                try:
                    delete_response = requests.delete(f"{qdrant_url}/collections/{collection_name}")
                    if delete_response.status_code in [200, 404]:  # 404 means already deleted
                        print(f"   ✅ Deleted collection: {collection_name}")
                        deleted_count += 1
                    else:
                        print(f"   ❌ Failed to delete {collection_name}: {delete_response.status_code}")
                except Exception as e:
                    print(f"   ❌ Error deleting {collection_name}: {e}")
            
            print(f"✅ Deleted {deleted_count}/{len(collection_names)} collections")
            
        else:
            print(f"❌ Failed to get collections: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Qdrant. Is it running on localhost:6333?")
    except Exception as e:
        print(f"❌ Error cleaning Qdrant: {e}")

def clean_django_database():
    """Clean all Django database tables."""
    print("\n🗑️  CLEANING DJANGO DATABASE")
    print("-" * 40)
    
    # Get counts before deletion
    search_results_count = SearchResult.objects.count()
    citations_count = ResultCitation.objects.count()
    search_queries_count = SearchQuery.objects.count()
    embedding_metadata_count = EmbeddingMetadata.objects.count()
    chunks_count = DocumentChunk.objects.count()
    documents_count = RawDocument.objects.count()
    
    print(f"Before cleanup:")
    print(f"   SearchResults: {search_results_count}")
    print(f"   Citations: {citations_count}")
    print(f"   SearchQueries: {search_queries_count}")
    print(f"   EmbeddingMetadata: {embedding_metadata_count}")
    print(f"   DocumentChunks: {chunks_count}")
    print(f"   Documents: {documents_count}")
    
    try:
        # Delete in proper order to avoid foreign key constraints
        print(f"\nDeleting data...")
        
        # 1. Delete search-related data
        ResultCitation.objects.all().delete()
        print(f"   ✅ Deleted all citations")
        
        SearchResult.objects.all().delete()
        print(f"   ✅ Deleted all search results")
        
        SearchQuery.objects.all().delete()
        print(f"   ✅ Deleted all search queries")
        
        # 2. Delete embedding metadata
        EmbeddingMetadata.objects.all().delete()
        print(f"   ✅ Deleted all embedding metadata")
        
        # 3. Delete document chunks
        DocumentChunk.objects.all().delete()
        print(f"   ✅ Deleted all document chunks")
        
        # 4. Delete documents
        RawDocument.objects.all().delete()
        print(f"   ✅ Deleted all documents")

        # Note: We keep DocumentSources as they represent data source configurations
        
        print(f"✅ Django database cleaned successfully")
        
    except Exception as e:
        print(f"❌ Error cleaning Django database: {e}")

def verify_cleanup():
    """Verify that cleanup was successful."""
    print("\n🔍 VERIFYING CLEANUP")
    print("-" * 40)
    
    # Check Django database
    remaining_counts = {
        'SearchResults': SearchResult.objects.count(),
        'Citations': ResultCitation.objects.count(),
        'SearchQueries': SearchQuery.objects.count(),
        'EmbeddingMetadata': EmbeddingMetadata.objects.count(),
        'DocumentChunks': DocumentChunk.objects.count(),
        'Documents': RawDocument.objects.count(),
        'Sources': DocumentSource.objects.count(),  # Should remain
    }
    
    print(f"Django database remaining:")
    for model, count in remaining_counts.items():
        status = "✅" if count == 0 or model == 'Sources' else "❌"
        print(f"   {status} {model}: {count}")
    
    # Check Qdrant
    try:
        qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
        qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
        qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
        
        response = requests.get(f"{qdrant_url}/collections")
        if response.status_code == 200:
            collections = response.json()
            collection_count = len(collections.get('result', {}).get('collections', []))
            status = "✅" if collection_count == 0 else "❌"
            print(f"   {status} Qdrant collections: {collection_count}")
        else:
            print(f"   ❌ Cannot verify Qdrant collections")
    except:
        print(f"   ❌ Cannot connect to Qdrant for verification")

def main():
    """Main cleanup function."""
    print("🧹 COMPREHENSIVE DATA CLEANUP")
    print("=" * 60)
    print("⚠️  WARNING: This will delete ALL data!")
    print("   - All documents and chunks")
    print("   - All search results and queries")
    print("   - All embeddings and vector data")
    print("   - All Qdrant collections")
    print()
    
    # Get confirmation
    confirmation = input("Are you sure you want to proceed? Type 'YES' to confirm: ")
    
    if confirmation != 'YES':
        print("❌ Cleanup cancelled")
        return
    
    print("\n🚀 Starting cleanup...")
    
    # Clean Qdrant first (in case Django cleanup fails)
    clean_qdrant_collections()
    
    # Clean Django database
    clean_django_database()
    
    # Verify cleanup
    verify_cleanup()
    
    print(f"\n🎉 CLEANUP COMPLETE!")
    print("=" * 60)
    print("Next steps:")
    print("1. Run data ingestion to populate fresh data")
    print("2. Test search functionality")
    print("3. Verify citations are working properly")

if __name__ == "__main__":
    main()
