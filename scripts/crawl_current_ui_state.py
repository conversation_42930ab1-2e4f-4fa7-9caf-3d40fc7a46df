#!/usr/bin/env python3
"""
Crawl and analyze the current UI state to understand exact issues.
"""

import os
import sys
import django
import requests
from bs4 import BeautifulSoup

# Add the project root to Python path
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)

# Change to the project directory
os.chdir(project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult

def crawl_ui_state():
    """Crawl the current UI state and analyze issues."""
    print("🕷️  CRAWLING CURRENT UI STATE")
    print("=" * 60)
    
    # Create a test client
    client = Client()
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'username': '<EMAIL>', 'first_name': 'Mahesh'}
    )
    
    # Login the user
    client.force_login(user)
    print(f"✅ User logged in: {user.email}")
    
    # Test the exact query from the screenshot
    query = "List issues reported by Amanda"
    print(f"\n🔍 Testing query: '{query}'")
    
    # Make the search request
    response = client.post('/search/query/', {
        'query': query,
        'use_hybrid_search': True,
        'use_context_aware': True,
        'use_query_expansion': False,
        'use_multi_step_reasoning': False
    })
    
    if response.status_code == 200:
        print("✅ Search request successful")
        
        # Get the latest search result
        latest_result = SearchResult.objects.latest('timestamp')
        
        # Parse the HTML response
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"\n📊 CURRENT STATE ANALYSIS:")
        print("-" * 40)
        
        # 1. Check confidence indicator
        confidence_indicator = soup.find('div', class_='confidence-indicator')
        if confidence_indicator:
            confidence_text = confidence_indicator.get_text(strip=True)
            print(f"🎯 Confidence: {confidence_text}")
            print(f"   LLM Score: {latest_result.llm_confidence_score}")
        
        # 2. Check response content structure
        response_container = soup.find('div', class_='professional-response-container')
        if response_container:
            response_content = response_container.find('div', class_='response-content')
            if response_content:
                print(f"\n📄 RESPONSE CONTENT:")
                print("-" * 30)
                print(response_content.get_text(strip=True)[:300] + "...")
                
                # Check for proper structure
                has_header = bool(response_content.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
                has_list = bool(response_content.find(['ul', 'ol']))
                has_list_items = bool(response_content.find('li'))
                
                print(f"\n🏗️  STRUCTURE ANALYSIS:")
                print(f"   Has Header: {'✅' if has_header else '❌'}")
                print(f"   Has List: {'✅' if has_list else '❌'}")
                print(f"   Has List Items: {'✅' if has_list_items else '❌'}")
        
        # 3. Check citations
        citations_section = soup.find('div', class_='professional-sources-section')
        if citations_section:
            source_cards = citations_section.find_all('div', class_='professional-source-card')
            print(f"\n📚 CITATIONS ANALYSIS:")
            print(f"   Total Citations: {len(source_cards)}")
            
            if source_cards:
                first_card = source_cards[0]
                
                # Check for date formatting
                date_element = first_card.find('div', class_='source-date')
                if date_element:
                    date_text = date_element.get_text(strip=True)
                    print(f"   Date Format: {date_text}")
                    is_humanized = any(word in date_text.lower() for word in ['ago', 'yesterday', 'today', 'last'])
                    print(f"   Is Humanized: {'✅' if is_humanized else '❌'}")
                
                # Check for clickable links
                source_link = first_card.find('a', class_='professional-source-link')
                print(f"   Has Clickable Links: {'✅' if source_link else '❌'}")
                
                # Check relevance scores
                relevance_element = first_card.find('span', class_='relevance-value')
                if relevance_element:
                    relevance_score = relevance_element.get_text(strip=True)
                    print(f"   Relevance Score: {relevance_score}")
        
        # 4. Raw response analysis
        print(f"\n📝 RAW RESPONSE ANALYSIS:")
        print(f"   Length: {len(latest_result.generated_answer)} characters")
        print(f"   Has Bullets: {'✅' if '•' in latest_result.generated_answer else '❌'}")
        print(f"   Has Citations: {'✅' if '[' in latest_result.generated_answer else '❌'}")
        
        # 5. Identify specific issues
        print(f"\n⚠️  IDENTIFIED ISSUES:")
        issues = []
        
        if latest_result.llm_confidence_score < 0.8:
            issues.append(f"Low confidence score: {latest_result.llm_confidence_score}")
        
        if len(latest_result.generated_answer) < 500:
            issues.append(f"Response too brief: {len(latest_result.generated_answer)} chars")
        
        if not has_header:
            issues.append("Missing proper header structure")
        
        if not has_list:
            issues.append("Missing list structure for bullet points")
        
        if not date_element or not is_humanized:
            issues.append("Missing or non-humanized dates")
        
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        if not issues:
            print("   ✅ No major issues detected!")
        
        return issues
        
    else:
        print(f"❌ Search request failed with status: {response.status_code}")
        return ["Search request failed"]

if __name__ == "__main__":
    issues = crawl_ui_state()
    print(f"\n🎯 TOTAL ISSUES FOUND: {len(issues)}")
    if issues:
        print("🔧 Ready to implement fixes...")
    else:
        print("🎉 UI is in good state!")
