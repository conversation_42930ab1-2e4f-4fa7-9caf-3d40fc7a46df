#!/usr/bin/env python3
"""
Test script to debug markdown filter processing.
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)

# Change to the project directory
os.chdir(project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.templatetags.search_extras import markdown_to_html

def test_markdown_filter():
    """Test the markdown filter with sample response content."""
    print("🔍 Testing Markdown Filter")
    print("=" * 50)
    
    # Sample response content similar to what we're getting
    sample_response = """Issues Reported by <PERSON>: • Additionally, she found a few bugs (paybands were off) and a few enhancements in the org view/insights/paybands in SDF [1]. • She also reported issues with login and a spinner when trying to log into sdf [2]. compiify in the staging environment [3]. • Finally, she noted that she was unable to upload data and that she was working a minimum of six hours a day to stay on top of issues and go back and forth with customers, needing more support or more hours to complete her work [4]."""
    
    print("📄 Original Response:")
    print("-" * 30)
    print(sample_response)
    print("-" * 30)
    
    # Test the markdown filter
    html_output = markdown_to_html(sample_response)
    
    print("\n📄 HTML Output:")
    print("-" * 30)
    print(html_output)
    print("-" * 30)
    
    # Test with proper markdown format
    markdown_sample = """## Issues Reported by Amanda

- Additionally, she found a few bugs (paybands were off) and a few enhancements in the org view/insights/paybands in SDF [1].
- She also reported issues with login and a spinner when trying to log into sdf [2]. compiify in the staging environment [3].
- Finally, she noted that she was unable to upload data and that she was working a minimum of six hours a day to stay on top of issues and go back and forth with customers, needing more support or more hours to complete her work [4]."""
    
    print("\n📄 Proper Markdown Format:")
    print("-" * 30)
    print(markdown_sample)
    print("-" * 30)
    
    html_output_proper = markdown_to_html(markdown_sample)
    
    print("\n📄 HTML Output (Proper Markdown):")
    print("-" * 30)
    print(html_output_proper)
    print("-" * 30)

    # Debug the detailed formatting detection
    print("\n🔍 Debug Information:")
    print("-" * 30)
    print(f"Has '•' character: {'•' in sample_response}")
    print(f"Length > 300: {len(sample_response) > 300}")
    print(f"Has 'Issues reported by': {'Issues reported by' in sample_response}")
    print(f"Has 'Issues reported by' (lowercase): {'Issues reported by' in sample_response.lower()}")
    print(f"Sample response lowercase: {sample_response.lower()[:100]}")
    print(f"Looking for: 'issues reported by'")
    print(f"Found: {'issues reported by' in sample_response.lower()}")
    print(f"Length: {len(sample_response)}")
    print(f"First 100 chars: {sample_response[:100]}")

    # Test the condition manually
    has_detailed_formatting = (
        "•" in sample_response and  # Has Unicode bullet points
        len(sample_response) > 300 and  # Is reasonably detailed
        ("issues reported by" in sample_response.lower() or "Issues Found:" in sample_response or
         "Summary of" in sample_response or "Information about" in sample_response)  # Has proper headers
    )
    print(f"Should trigger detailed formatting: {has_detailed_formatting}")
    print("-" * 30)

if __name__ == "__main__":
    test_markdown_filter()
