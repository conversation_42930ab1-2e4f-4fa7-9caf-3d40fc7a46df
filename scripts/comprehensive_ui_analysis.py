#!/usr/bin/env python3
"""
Comprehensive end-to-end analysis and testing of RAG system.
Identifies all issues from ingestion to UI rendering.
"""

import os
import sys
import django
import time
import json
from datetime import datetime

# Add the project root to Python path and change directory
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult
from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant

def analyze_rag_pipeline():
    """Comprehensive analysis of the entire RAG pipeline."""
    print("🔍 COMPREHENSIVE RAG PIPELINE ANALYSIS")
    print("=" * 60)
    
    issues = []
    
    # 1. Test Backend RAG Service
    print("\n1️⃣ TESTING BACKEND RAG SERVICE")
    print("-" * 40)
    
    try:
        user = User.objects.get(email='<EMAIL>')
        tenant = Tenant.objects.first()
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        
        # Test query from screenshot
        query = "List issues reported by Amanda"
        print(f"Query: {query}")
        
        start_time = time.time()
        search_result, retrieved_docs = rag_service.search(
            query_text=query,
            top_k=20,
            min_relevance_score=0.1,
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )
        processing_time = time.time() - start_time
        
        print(f"✅ Search completed in {processing_time:.2f}s")
        print(f"📊 Retrieved docs: {len(retrieved_docs) if retrieved_docs else 0}")
        print(f"📊 Citations: {search_result.citations.count()}")
        print(f"🎯 Confidence: {search_result.llm_confidence_score:.3f}")
        print(f"📝 Response length: {len(search_result.generated_answer)} chars")
        
        # Analyze response quality
        response = search_result.generated_answer
        has_bullets = '•' in response or '-' in response or any(f"{i}." in response for i in range(1, 10))
        has_structure = any(word in response.lower() for word in ['summary', 'details', 'issues', 'reported'])
        has_citations = '[' in response and ']' in response
        
        print(f"\n📋 RESPONSE ANALYSIS:")
        print(f"   Has bullet points: {'✅' if has_bullets else '❌'}")
        print(f"   Has structure: {'✅' if has_structure else '❌'}")
        print(f"   Has citations: {'✅' if has_citations else '❌'}")
        print(f"   Response preview: {response[:200]}...")
        
        if search_result.llm_confidence_score < 0.8:
            issues.append(f"Low confidence score: {search_result.llm_confidence_score:.3f}")
        
        if len(response) < 500:
            issues.append(f"Response too brief: {len(response)} characters")
        
        if not has_bullets:
            issues.append("Missing bullet point structure")
        
        if not has_citations:
            issues.append("Missing proper citations")
            
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        issues.append(f"Backend service error: {e}")
    
    return issues

def analyze_django_views():
    """Test Django views and HTML rendering."""
    print("\n2️⃣ TESTING DJANGO VIEWS")
    print("-" * 40)
    
    issues = []
    
    try:
        user = User.objects.get(email='<EMAIL>')
        client = Client()
        client.force_login(user)
        
        query = "List issues reported by Amanda"
        response = client.post('/search/query/', {
            'query': query,
            'use_hybrid_search': True,
            'use_context_aware': True,
            'use_query_expansion': False,
            'use_multi_step_reasoning': False
        })
        
        print(f"✅ View response status: {response.status_code}")
        
        if response.status_code != 200:
            issues.append(f"View returned status {response.status_code}")
            return issues
        
        # Get the latest search result
        latest_result = SearchResult.objects.latest('timestamp')
        
        # Analyze the search result
        print(f"🎯 Confidence: {latest_result.llm_confidence_score:.3f}")
        print(f"📝 Response length: {len(latest_result.generated_answer)} chars")
        print(f"📚 Citations count: {latest_result.citations.count()}")
        
        # Check response content
        response_text = latest_result.generated_answer
        
        # Check for detailed structure
        has_summary = 'summary' in response_text.lower()
        has_bullet_points = any(marker in response_text for marker in ['•', '- ', '* ']) or any(f"{i}." in response_text for i in range(1, 10))
        has_details = len(response_text) > 500
        has_proper_citations = '[' in response_text and ']' in response_text
        
        print(f"\n📋 CONTENT ANALYSIS:")
        print(f"   Has summary: {'✅' if has_summary else '❌'}")
        print(f"   Has bullet points: {'✅' if has_bullet_points else '❌'}")
        print(f"   Has sufficient detail: {'✅' if has_details else '❌'}")
        print(f"   Has proper citations: {'✅' if has_proper_citations else '❌'}")
        
        # Check citations for humanized dates
        citations = latest_result.citations.all()
        if citations:
            first_citation = citations[0]
            doc_date = first_citation.document_chunk.document.created_at
            print(f"📅 First citation date: {doc_date}")
            
            # Check if we have humanized date logic
            from django.utils.timesince import timesince
            humanized = timesince(doc_date)
            print(f"📅 Humanized: {humanized} ago")
        
        # Identify issues
        if latest_result.llm_confidence_score < 0.8:
            issues.append(f"Low confidence score: {latest_result.llm_confidence_score:.3f}")
        
        if not has_details:
            issues.append(f"Response too brief: {len(response_text)} characters")
        
        if not has_bullet_points:
            issues.append("Missing bullet point structure in response")
        
        if not has_summary:
            issues.append("Missing summary structure in response")
        
        if not has_proper_citations:
            issues.append("Missing proper citation formatting")
        
    except Exception as e:
        print(f"❌ View test failed: {e}")
        issues.append(f"View error: {e}")
    
    return issues

def generate_fix_plan(backend_issues, view_issues):
    """Generate a comprehensive fix plan."""
    print(f"\n🔧 COMPREHENSIVE FIX PLAN")
    print("=" * 60)
    
    all_issues = backend_issues + view_issues
    
    if not all_issues:
        print("🎉 No issues found! System is working correctly.")
        return
    
    print(f"📋 Total Issues Found: {len(all_issues)}")
    
    print(f"\n🔧 BACKEND ISSUES:")
    for i, issue in enumerate(backend_issues, 1):
        print(f"   {i}. {issue}")
    
    print(f"\n🎨 VIEW/UI ISSUES:")
    for i, issue in enumerate(view_issues, 1):
        print(f"   {i}. {issue}")
    
    # Generate specific fix recommendations
    print(f"\n📝 RECOMMENDED FIXES:")
    
    fix_counter = 1
    
    if any('confidence' in issue.lower() for issue in all_issues):
        print(f"   {fix_counter}. Improve LLM prompt templates for higher confidence scores")
        print(f"   {fix_counter + 1}. Adjust confidence thresholds in UI display logic")
        fix_counter += 2
    
    if any('brief' in issue.lower() for issue in all_issues):
        print(f"   {fix_counter}. Enhance response generation to include more detailed content")
        print(f"   {fix_counter + 1}. Improve bullet point formatting in response templates")
        fix_counter += 2
    
    if any('bullet' in issue.lower() for issue in all_issues):
        print(f"   {fix_counter}. Fix bullet point structure in response formatter")
        fix_counter += 1
    
    if any('citation' in issue.lower() for issue in all_issues):
        print(f"   {fix_counter}. Improve citation formatting and display")
        print(f"   {fix_counter + 1}. Implement humanized date formatting")
        fix_counter += 2
    
    return all_issues

if __name__ == "__main__":
    # Run comprehensive analysis
    backend_issues = analyze_rag_pipeline()
    view_issues = analyze_django_views()
    
    all_issues = generate_fix_plan(backend_issues, view_issues)
    
    # Save results for reference
    results = {
        'timestamp': datetime.now().isoformat(),
        'backend_issues': backend_issues,
        'view_issues': view_issues,
        'total_issues': len(all_issues) if all_issues else 0
    }
    
    with open('comprehensive_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to comprehensive_analysis_results.json")
