#!/usr/bin/env python3
"""
Test script to check UI response content and formatting.
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)

# Change to the project directory
os.chdir(project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

from apps.search.models import SearchResult

def test_ui_response_content():
    """Test the UI response content and formatting."""
    print("🔍 Testing UI Response Content")
    print("=" * 50)
    
    # Create a test client
    client = Client()
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'username': '<EMAIL>', 'first_name': 'Mahesh'}
    )
    
    # Login the user
    client.force_login(user)
    print(f"✅ User logged in: {user.email}")
    
    # Test query
    query = "List issues reported by <PERSON>"
    print(f"\n🔍 Testing search with query: '{query}'")
    
    # Make the search request
    response = client.post('/search/query/', {
        'query': query,
        'use_hybrid_search': True,
        'use_context_aware': True,
        'use_query_expansion': False,
        'use_multi_step_reasoning': False
    })
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Search request successful")
        
        # Get the latest search result
        latest_result = SearchResult.objects.latest('timestamp')
        print(f"\n📊 Latest search result:")
        print(f"   ID: {latest_result.id}")
        print(f"   Query: {latest_result.search_query.query_text}")
        print(f"   User: {latest_result.user.email}")
        print(f"   Citations: {latest_result.citations.count()}")
        
        # Print the raw response content
        print(f"\n📄 Raw Generated Answer:")
        print("-" * 50)
        print(latest_result.generated_answer)
        print("-" * 50)
        
        # Check the HTML response content
        content = response.content.decode('utf-8')
        
        # Look for the response content in the HTML
        if 'professional-response-container' in content:
            print("✅ Found response container in HTML")
            
            # Extract the response content section
            start_marker = 'professional-response-container'
            end_marker = '</div>'
            
            start_idx = content.find(start_marker)
            if start_idx != -1:
                # Find the actual content within the response container
                response_start = content.find('response-content', start_idx)
                if response_start != -1:
                    # Find the end of the response content div
                    div_count = 0
                    current_pos = response_start
                    while current_pos < len(content):
                        if content[current_pos:current_pos+4] == '<div':
                            div_count += 1
                        elif content[current_pos:current_pos+6] == '</div>':
                            div_count -= 1
                            if div_count == 0:
                                response_end = current_pos + 6
                                break
                        current_pos += 1
                    
                    if 'response_end' in locals():
                        response_html = content[response_start:response_end]
                        print(f"\n📄 Response HTML Content:")
                        print("-" * 50)
                        print(response_html[:1000] + "..." if len(response_html) > 1000 else response_html)
                        print("-" * 50)
                        
                        # Check if the response contains actual content
                        if latest_result.generated_answer.strip() in response_html:
                            print("✅ Response content is properly displayed in HTML")
                        else:
                            print("❌ Response content is NOT properly displayed in HTML")
                            print(f"Expected content: {latest_result.generated_answer[:200]}...")
                    else:
                        print("❌ Could not find end of response content div")
                else:
                    print("❌ Could not find response-content div")
            else:
                print("❌ Could not find response container content")
        else:
            print("❌ Response container not found in HTML")
            
        # Check for citations
        citations_count = latest_result.citations.count()
        if citations_count > 0:
            print(f"✅ Found {citations_count} citations")
            
            # Check if citations are in the HTML
            if 'professional-source-card' in content:
                print("✅ Citations are displayed in HTML")
            else:
                print("❌ Citations are NOT displayed in HTML")
        else:
            print("❌ No citations found")
            
    else:
        print(f"❌ Search request failed with status: {response.status_code}")
        print(f"Response content: {response.content.decode('utf-8')[:500]}...")

if __name__ == "__main__":
    test_ui_response_content()
