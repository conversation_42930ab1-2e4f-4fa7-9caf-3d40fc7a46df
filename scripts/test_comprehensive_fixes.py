#!/usr/bin/env python3
"""
Test comprehensive fixes for RAG system UI and backend improvements.
"""

import os
import sys
import django
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.search.models import SearchResult
from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant

def test_backend_improvements():
    """Test backend improvements."""
    print("🔧 TESTING BACKEND IMPROVEMENTS")
    print("=" * 50)
    
    user = User.objects.get(email='<EMAIL>')
    tenant = Tenant.objects.first()
    rag_service = RAGService(user=user, tenant_slug=tenant.slug)
    
    query = "List issues reported by Amanda"
    print(f"Query: {query}")
    
    start_time = time.time()
    search_result, retrieved_docs = rag_service.search(
        query_text=query,
        top_k=20,
        min_relevance_score=0.1,
        use_hybrid_search=True,
        use_context_aware=True
    )
    processing_time = time.time() - start_time
    
    print(f"✅ Processing time: {processing_time:.2f}s")
    print(f"📊 Retrieved docs: {len(retrieved_docs)}")
    print(f"📚 Citations: {search_result.citations.count()}")
    print(f"🎯 Confidence: {search_result.llm_confidence_score:.3f}")
    print(f"📝 Response length: {len(search_result.generated_answer)} chars")
    
    # Check improvements
    improvements = []
    
    if search_result.llm_confidence_score >= 0.8:
        improvements.append("✅ High confidence score achieved")
    elif search_result.llm_confidence_score >= 0.7:
        improvements.append("✅ Good confidence score achieved")
    else:
        improvements.append("❌ Confidence score still needs improvement")
    
    if len(search_result.generated_answer) > 1000:
        improvements.append("✅ Detailed response generated")
    else:
        improvements.append("❌ Response could be more detailed")
    
    if '•' in search_result.generated_answer:
        improvements.append("✅ Bullet point structure present")
    else:
        improvements.append("❌ Missing bullet point structure")
    
    if search_result.citations.count() > 0:
        improvements.append("✅ Citations created successfully")
    else:
        improvements.append("❌ No citations created")
    
    print(f"\n📋 BACKEND IMPROVEMENTS:")
    for improvement in improvements:
        print(f"   {improvement}")
    
    return search_result

def test_ui_improvements():
    """Test UI improvements with headless browser."""
    print("\n🎨 TESTING UI IMPROVEMENTS")
    print("=" * 50)
    
    try:
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_window_size(1920, 1080)
        
        try:
            # Navigate to search page
            driver.get("http://localhost:8000/search/")
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find search input and submit query
            search_input = driver.find_element(By.NAME, "query")
            search_input.send_keys("List issues reported by Amanda")
            
            # Submit form
            submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            submit_button.click()
            
            # Wait for results
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-card"))
            )
            
            # Check UI improvements
            improvements = []
            
            # Check confidence indicator
            confidence_elements = driver.find_elements(By.CLASS_NAME, "confidence-indicator")
            if confidence_elements:
                confidence_text = confidence_elements[0].text
                print(f"🎯 UI Confidence: {confidence_text}")
                
                if "High Confidence" in confidence_text:
                    improvements.append("✅ High confidence displayed")
                elif "Good Confidence" in confidence_text:
                    improvements.append("✅ Good confidence displayed")
                elif "Medium Confidence" in confidence_text:
                    improvements.append("⚠️ Medium confidence displayed")
                else:
                    improvements.append("❌ Low confidence displayed")
            
            # Check response structure
            response_elements = driver.find_elements(By.CLASS_NAME, "response-content")
            if response_elements:
                response_text = response_elements[0].text
                print(f"📝 Response length: {len(response_text)} chars")
                
                if len(response_text) > 1000:
                    improvements.append("✅ Detailed response in UI")
                else:
                    improvements.append("❌ Response too brief in UI")
                
                if "•" in response_text or "Issue" in response_text:
                    improvements.append("✅ Structured content in UI")
                else:
                    improvements.append("❌ Missing structure in UI")
            
            # Check citations
            citation_elements = driver.find_elements(By.CLASS_NAME, "professional-source-card")
            print(f"📚 Citation cards: {len(citation_elements)}")
            
            if len(citation_elements) > 0:
                improvements.append("✅ Citations displayed in UI")
                
                # Check first citation for humanized dates
                first_citation = citation_elements[0]
                date_elements = first_citation.find_elements(By.CLASS_NAME, "source-date")
                if date_elements:
                    date_text = date_elements[0].text
                    if "ago" in date_text.lower():
                        improvements.append("✅ Humanized dates in citations")
                    else:
                        improvements.append("❌ Non-humanized dates in citations")
                
                # Check for clickable links
                links = first_citation.find_elements(By.TAG_NAME, "a")
                if links:
                    improvements.append("✅ Clickable citation links")
                else:
                    improvements.append("❌ Missing clickable citation links")
            else:
                improvements.append("❌ No citations displayed in UI")
            
            print(f"\n📋 UI IMPROVEMENTS:")
            for improvement in improvements:
                print(f"   {improvement}")
            
            return improvements
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        return [f"❌ UI test error: {e}"]

def generate_summary_report(backend_result, ui_improvements):
    """Generate a comprehensive summary report."""
    print(f"\n📊 COMPREHENSIVE IMPROVEMENT SUMMARY")
    print("=" * 60)
    
    # Backend metrics
    print(f"🔧 BACKEND METRICS:")
    print(f"   Confidence Score: {backend_result.llm_confidence_score:.3f}")
    print(f"   Response Length: {len(backend_result.generated_answer):,} characters")
    print(f"   Citations Created: {backend_result.citations.count()}")
    print(f"   Has Structure: {'✅' if '•' in backend_result.generated_answer else '❌'}")
    
    # UI metrics
    print(f"\n🎨 UI METRICS:")
    ui_success_count = len([imp for imp in ui_improvements if imp.startswith("✅")])
    ui_total_count = len(ui_improvements)
    ui_success_rate = (ui_success_count / ui_total_count * 100) if ui_total_count > 0 else 0
    print(f"   UI Success Rate: {ui_success_rate:.1f}% ({ui_success_count}/{ui_total_count})")
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    
    if backend_result.llm_confidence_score >= 0.8:
        print("   ✅ Confidence scoring significantly improved")
    elif backend_result.llm_confidence_score >= 0.7:
        print("   ✅ Confidence scoring improved")
    else:
        print("   ❌ Confidence scoring needs more work")
    
    if len(backend_result.generated_answer) > 1000:
        print("   ✅ Response detail significantly improved")
    else:
        print("   ❌ Response detail needs improvement")
    
    if ui_success_rate >= 80:
        print("   ✅ UI improvements successful")
    elif ui_success_rate >= 60:
        print("   ⚠️ UI improvements partially successful")
    else:
        print("   ❌ UI improvements need more work")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if backend_result.citations.count() == 0:
        print("   • Fix citation lookup issues for better source attribution")
    
    if backend_result.llm_confidence_score < 0.8:
        print("   • Further tune confidence scoring algorithm")
    
    if ui_success_rate < 80:
        print("   • Address remaining UI display issues")
    
    print("   • Continue monitoring and iterating on improvements")

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE RAG SYSTEM IMPROVEMENT TEST")
    print("=" * 60)
    
    # Test backend improvements
    backend_result = test_backend_improvements()
    
    # Test UI improvements
    ui_improvements = test_ui_improvements()
    
    # Generate summary report
    generate_summary_report(backend_result, ui_improvements)
    
    print(f"\n✅ Comprehensive testing completed!")
