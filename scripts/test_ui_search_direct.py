#!/usr/bin/env python
"""
Test the UI search functionality directly by calling the Django view.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult

def test_ui_search():
    """Test the UI search functionality."""
    print("🔍 Testing UI Search Functionality")
    print("=" * 50)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"Using user: {user.email}")
    
    # Create a test client
    client = Client()
    
    # Log in the user
    client.force_login(user)
    print("✅ User logged in")
    
    # Test the search endpoint
    search_data = {
        'query': 'List issues reported by <PERSON>',
        'sources': 'all',
        'output_format': 'text'
    }
    
    print(f"\n🔍 Testing search with query: '{search_data['query']}'")
    
    # Get the count of search results before
    before_count = SearchResult.objects.count()
    print(f"Search results before: {before_count}")
    
    # Make the search request
    response = client.post('/search/query/', search_data)
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Search request successful")
        
        # Check if new search results were created
        after_count = SearchResult.objects.count()
        print(f"Search results after: {after_count}")
        
        if after_count > before_count:
            # Get the latest search result
            latest_result = SearchResult.objects.latest('timestamp')
            print(f"\n📊 Latest search result:")
            print(f"   ID: {latest_result.id}")
            print(f"   Query: {latest_result.search_query.query_text}")
            print(f"   User: {latest_result.user.email}")
            print(f"   Tenant: {latest_result.search_query.tenant.slug}")
            print(f"   Citations: {latest_result.citations.count()}")
            
            # Check citations
            citations = latest_result.citations.all()
            if citations.exists():
                print(f"\n📖 Citations:")
                for i, citation in enumerate(citations[:5]):
                    print(f"   {i+1}. Chunk {citation.document_chunk.id} (score: {citation.relevance_score:.3f})")
                    if citation.document_chunk.document:
                        doc = citation.document_chunk.document
                        print(f"      Document: {doc.title}")
                        if doc.permalink:
                            print(f"      Permalink: {doc.permalink}")
            else:
                print("❌ No citations found")
                
            # Check the response content
            print(f"\n📄 Answer preview: {latest_result.generated_answer[:200]}...")
            
        else:
            print("❌ No new search results created")
    
    elif response.status_code == 302:
        print(f"🔄 Redirect to: {response.url}")
        
        # Follow the redirect
        redirect_response = client.get(response.url)
        print(f"Redirect response status: {redirect_response.status_code}")
        
        if redirect_response.status_code == 200:
            # Check if we're on the search results page
            content = redirect_response.content.decode('utf-8')
            if 'search-results' in content:
                print("✅ Redirected to search results page")
                
                # Check if there are results displayed
                if 'No results found' in content:
                    print("❌ No results displayed on page")
                else:
                    print("✅ Results displayed on page")
                    
                    # Check for citations
                    if 'citation' in content.lower():
                        print("✅ Citations appear to be displayed")
                    else:
                        print("❌ No citations displayed")
            else:
                print("❌ Not redirected to search results page")
    else:
        print(f"❌ Search request failed with status {response.status_code}")
        print(f"Response content: {response.content.decode('utf-8')[:500]}")

if __name__ == "__main__":
    test_ui_search()
