#!/usr/bin/env python3
"""
Test final improvements for RAG system.
"""

import os
import sys
import django
import time

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult
from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant

def test_improvements():
    """Test all improvements."""
    print("🚀 TESTING COMPREHENSIVE IMPROVEMENTS")
    print("=" * 60)
    
    # Test backend
    print("\n1️⃣ BACKEND TEST")
    print("-" * 30)
    
    user = User.objects.get(email='<EMAIL>')
    tenant = Tenant.objects.first()
    rag_service = RAGService(user=user, tenant_slug=tenant.slug)
    
    query = "List issues reported by <PERSON>"
    print(f"Query: {query}")
    
    start_time = time.time()
    search_result, retrieved_docs = rag_service.search(
        query_text=query,
        top_k=20,
        min_relevance_score=0.1,
        use_hybrid_search=True,
        use_context_aware=True
    )
    processing_time = time.time() - start_time
    
    print(f"✅ Processing time: {processing_time:.2f}s")
    print(f"📊 Retrieved docs: {len(retrieved_docs)}")
    print(f"🎯 Confidence: {search_result.llm_confidence_score:.3f}")
    print(f"📝 Response length: {len(search_result.generated_answer):,} chars")
    
    # Test Django view
    print("\n2️⃣ DJANGO VIEW TEST")
    print("-" * 30)
    
    client = Client()
    client.force_login(user)
    
    response = client.post('/search/query/', {
        'query': query,
        'use_hybrid_search': True,
        'use_context_aware': True,
        'use_query_expansion': False,
        'use_multi_step_reasoning': False
    })
    
    print(f"✅ View status: {response.status_code}")
    
    # Get latest result
    latest_result = SearchResult.objects.latest('timestamp')
    print(f"🎯 View confidence: {latest_result.llm_confidence_score:.3f}")
    print(f"📚 Citations: {latest_result.citations.count()}")
    
    # Analysis
    print("\n3️⃣ IMPROVEMENT ANALYSIS")
    print("-" * 30)
    
    improvements = []
    
    # Confidence improvements
    if latest_result.llm_confidence_score >= 0.85:
        improvements.append("✅ Excellent confidence score (≥0.85)")
    elif latest_result.llm_confidence_score >= 0.70:
        improvements.append("✅ Good confidence score (≥0.70)")
    elif latest_result.llm_confidence_score >= 0.50:
        improvements.append("⚠️ Medium confidence score (≥0.50)")
    else:
        improvements.append("❌ Low confidence score (<0.50)")
    
    # Response quality
    response_text = latest_result.generated_answer
    if len(response_text) > 1500:
        improvements.append("✅ Very detailed response (>1500 chars)")
    elif len(response_text) > 800:
        improvements.append("✅ Detailed response (>800 chars)")
    else:
        improvements.append("❌ Brief response (<800 chars)")
    
    # Structure
    if '•' in response_text and 'Issues reported by' in response_text:
        improvements.append("✅ Excellent structure with bullets and headers")
    elif '•' in response_text:
        improvements.append("✅ Good structure with bullet points")
    else:
        improvements.append("❌ Missing bullet point structure")
    
    # Citations
    citation_count = latest_result.citations.count()
    if citation_count >= 10:
        improvements.append("✅ Excellent citation count (≥10)")
    elif citation_count >= 5:
        improvements.append("✅ Good citation count (≥5)")
    elif citation_count > 0:
        improvements.append("⚠️ Some citations created")
    else:
        improvements.append("❌ No citations created")
    
    # Content quality indicators
    has_dates = any(month in response_text for month in ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'])
    has_names = 'Amanda' in response_text
    has_details = len(response_text.split('.')) > 10  # More than 10 sentences
    
    if has_dates:
        improvements.append("✅ Contains specific dates")
    if has_names:
        improvements.append("✅ Contains specific names")
    if has_details:
        improvements.append("✅ Contains detailed information")
    
    print("📋 IMPROVEMENTS ACHIEVED:")
    for improvement in improvements:
        print(f"   {improvement}")
    
    # Summary
    print("\n4️⃣ SUMMARY")
    print("-" * 30)
    
    success_count = len([imp for imp in improvements if imp.startswith("✅")])
    total_count = len(improvements)
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    
    print(f"📊 Success Rate: {success_rate:.1f}% ({success_count}/{total_count})")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT: Major improvements achieved!")
    elif success_rate >= 60:
        print("✅ GOOD: Significant improvements made")
    elif success_rate >= 40:
        print("⚠️ FAIR: Some improvements made")
    else:
        print("❌ POOR: More work needed")
    
    # Key metrics comparison
    print(f"\n📈 KEY METRICS:")
    print(f"   Confidence Score: {latest_result.llm_confidence_score:.3f} (Target: ≥0.70)")
    print(f"   Response Length: {len(response_text):,} chars (Target: ≥800)")
    print(f"   Citation Count: {citation_count} (Target: ≥5)")
    print(f"   Has Structure: {'✅' if '•' in response_text else '❌'}")
    print(f"   Has Details: {'✅' if has_details else '❌'}")
    
    # UI confidence display prediction
    if latest_result.llm_confidence_score >= 0.85:
        ui_display = "High Confidence Response"
    elif latest_result.llm_confidence_score >= 0.70:
        ui_display = "Good Confidence Response"
    elif latest_result.llm_confidence_score >= 0.50:
        ui_display = "Medium Confidence Response"
    else:
        ui_display = "Low Confidence Response"
    
    print(f"\n🎯 UI WILL DISPLAY: '{ui_display}'")
    
    # Response preview
    print(f"\n📄 RESPONSE PREVIEW:")
    preview = response_text[:300] + "..." if len(response_text) > 300 else response_text
    print(f"   {preview}")
    
    return latest_result

if __name__ == "__main__":
    result = test_improvements()
    print(f"\n✅ Testing completed!")
