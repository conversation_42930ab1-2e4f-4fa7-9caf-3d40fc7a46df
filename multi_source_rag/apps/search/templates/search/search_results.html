{% extends 'base.html' %}

{% load search_extras %}

{% block title %}Search Results - Multi-Source RAG{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/search_results.css">
<link rel="stylesheet" href="/static/css/modern_ui.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="modern-search-container">
    <!-- Header Section -->
    <div class="search-header">
        <div class="search-breadcrumb">
            <a href="/" class="breadcrumb-link">Home</a>
            <span class="breadcrumb-separator">›</span>
            <a href="{% url 'search:search' %}" class="breadcrumb-link">Search</a>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-current">Results</span>
        </div>

        <div class="search-actions">
            <a href="{% url 'search:search' %}" class="modern-btn modern-btn-primary">
                <i class="bi bi-plus-circle"></i>
                New Search
            </a>
            <a href="{% url 'search:conversations' %}" class="modern-btn modern-btn-secondary">
                <i class="bi bi-chat-text"></i>
                Conversations
            </a>
        </div>
    </div>

<div class="row">
    <div class="col-lg-8">
        <!-- Query Card -->
        <div class="search-card mb-4">
            <div class="search-card-header d-flex justify-content-between align-items-center">
                <div class="fw-bold">
                    <i class="bi bi-search me-2"></i>Query
                </div>
                <span class="badge bg-primary">{{ citations|length }} sources</span>
            </div>
            <div class="search-card-body">
                <p class="mb-0 fs-5">{{ query }}</p>
            </div>
        </div>

        <!-- Response Card -->
        <div class="search-card mb-4">
            <div class="search-card-header d-flex justify-content-between align-items-center">
                <div class="fw-bold">
                    <i class="bi bi-robot me-2"></i>AI Response & Analysis
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-success">{{ citations|length }} sources</span>
                    <span class="badge bg-secondary" title="Processing time">{{ search_result.timestamp|timesince }}</span>
                </div>
            </div>
            <div class="search-card-body">
                <!-- Confidence Indicator -->
                <div class="confidence-section mb-3">
                    {% if search_result.llm_confidence_score >= 0.85 %}
                        <div class="confidence-indicator confidence-high">
                            <i class="bi bi-check-circle-fill"></i>
                            High Confidence Response
                        </div>
                    {% elif search_result.llm_confidence_score >= 0.70 %}
                        <div class="confidence-indicator confidence-medium">
                            <i class="bi bi-info-circle-fill"></i>
                            Good Confidence Response
                        </div>
                    {% elif search_result.llm_confidence_score >= 0.50 %}
                        <div class="confidence-indicator confidence-medium">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                            Medium Confidence Response
                        </div>
                    {% else %}
                        <div class="confidence-indicator confidence-low">
                            <i class="bi bi-question-circle-fill"></i>
                            Low Confidence Response
                        </div>
                    {% endif %}
                </div>

                <!-- Enhanced Answer Content -->
                <div class="professional-response-container" id="response-text">
                    <div class="response-content">
                        {{ response|markdown_to_html|safe }}
                    </div>
                </div>

                <!-- Enhanced Sources Section -->
                <div class="professional-sources-section">
                    <div class="sources-header">
                        <h4 class="sources-title">
                            <i class="bi bi-bookmark-star-fill me-2"></i>
                            Referenced Sources
                        </h4>
                        <p class="sources-subtitle">Click on any citation number in the response above to highlight the corresponding source</p>
                    </div>

                    <div class="professional-sources-grid">
                        {% for citation in citations %}
                            <div id="source-{{ citation.id }}" class="professional-source-card" data-citation-id="{{ forloop.counter }}">
                                <div class="source-card-header">
                                    <div class="citation-number-large">{{ forloop.counter }}</div>
                                    <div class="source-type-info">
                                        {% if citation.document_chunk.document.source %}
                                            <span class="professional-source-badge source-badge-{{ citation.document_chunk.document.source.source_type }}">
                                                <i class="bi bi-{% if citation.document_chunk.document.source.source_type == 'slack' %}slack{% elif citation.document_chunk.document.source.source_type == 'github' %}github{% elif citation.document_chunk.document.source.source_type == 'confluence' %}journal-text{% else %}file-text{% endif %}"></i>
                                                {{ citation.document_chunk.document.source.source_type|title }}
                                            </span>
                                        {% endif %}
                                        <div class="relevance-score">
                                            <span class="relevance-label">Relevance:</span>
                                            <span class="relevance-value">{{ citation.relevance_score|floatformat:2 }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="source-card-body">
                                    <h6 class="source-document-title">
                                        {{ citation.document_chunk.document.title|default:"Untitled Document"|truncatechars:60 }}
                                    </h6>
                                    <div class="source-content-preview">
                                        {{ citation.document_chunk.text|truncatechars:150 }}
                                    </div>

                                    {% if citation.document_chunk.document.created_at %}
                                        <div class="source-date">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ citation.document_chunk.document.created_at|date:"M d, Y" }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="source-card-footer">
                                    {% if citation.document_chunk.document.permalink %}
                                        <a href="{{ citation.document_chunk.document.permalink }}" target="_blank" class="professional-source-link">
                                            <i class="bi bi-box-arrow-up-right me-1"></i>
                                            View Original
                                        </a>
                                    {% else %}
                                        <span class="text-muted small">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Source not available
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        {% empty %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                No sources found for this answer.
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Follow-up Card -->
        <div class="search-card">
            <div class="search-card-header">
                <div class="fw-bold">
                    <i class="bi bi-chat-dots me-2"></i>Ask a follow-up question
                </div>
            </div>
            <div class="search-card-body">
                <!-- Suggested follow-up questions -->
                <div class="follow-up-suggestions">
                    <div class="follow-up-suggestion" onclick="fillFollowUpQuestion('Can you explain more about the database schema?')">
                        <i class="bi bi-chat-square-text me-1"></i> Database schema details
                    </div>
                    <div class="follow-up-suggestion" onclick="fillFollowUpQuestion('How does this relate to our current project?')">
                        <i class="bi bi-chat-square-text me-1"></i> Relevance to current project
                    </div>
                    <div class="follow-up-suggestion" onclick="fillFollowUpQuestion('Can you provide more examples?')">
                        <i class="bi bi-chat-square-text me-1"></i> More examples
                    </div>
                </div>

                <form method="post" action="{% url 'search:query' %}" id="follow-up-form">
                    {% csrf_token %}
                    <input type="hidden" name="conversation_id" value="{{ conversation.id }}">
                    <div class="input-group">
                        <input type="text" class="form-control" id="query" name="query" placeholder="Type your follow-up question..." required>
                        <button type="submit" class="btn btn-primary d-flex align-items-center gap-2">
                            <i class="bi bi-send"></i> Send
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Conversation History Card -->
        <div class="search-card mb-4">
            <div class="search-card-header d-flex justify-content-between align-items-center">
                <div class="fw-bold">
                    <i class="bi bi-chat-left-text me-2"></i>Conversation History
                </div>
                <span class="badge bg-secondary">{{ conversation.messages.all|length }}</span>
            </div>
            <div class="search-card-body p-0">
                <div class="list-group list-group-flush">
                    {% for message in conversation.messages.all %}
                        <div class="list-group-item {% if message.is_user %}bg-light{% endif %}">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <strong>{% if message.is_user %}You{% else %}Assistant{% endif %}</strong>
                                <small class="text-muted">{{ message.created_at|date:"M d, H:i" }}</small>
                            </div>
                            <p class="mb-0 small">{{ message.content|truncatechars:150 }}</p>
                        </div>
                    {% empty %}
                        <div class="list-group-item">No messages yet.</div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Conversations Card -->
        <div class="search-card">
            <div class="search-card-header">
                <div class="fw-bold">
                    <i class="bi bi-clock-history me-2"></i>Recent Conversations
                </div>
            </div>
            <div class="search-card-body p-0">
                <div class="list-group list-group-flush">
                    {% for recent_conv in recent_conversations %}
                        <a href="{% url 'search:conversation_detail' recent_conv.id %}" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>{{ recent_conv.title|truncatechars:40 }}</span>
                                <small class="text-muted">{{ recent_conv.updated_at|date:"M d" }}</small>
                            </div>
                        </a>
                    {% empty %}
                        <div class="list-group-item">No recent conversations.</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Process response text to add citation numbers
        processResponseWithCitations();

        // Setup source item interactions
        setupSourceInteractions();
    });

    // Process the response text to add citation numbers (preserving markdown formatting)
    function processResponseWithCitations() {
        const responseText = document.getElementById('response-text');
        if (!responseText) return;

        // Get all source items - FIXED: Use correct class name
        const sourceItems = document.querySelectorAll('.professional-source-card');
        if (sourceItems.length === 0) return;

        // Get the response content div
        const responseContent = responseText.querySelector('.response-content');
        if (!responseContent) return;

        // Process existing citation numbers in the text
        let content = responseContent.innerHTML;

        // Look for existing citation patterns like [1], [2], etc.
        const citationPattern = /\[(\d+)\]/g;
        let match;
        const existingCitations = [];

        while ((match = citationPattern.exec(content)) !== null) {
            existingCitations.push({
                number: parseInt(match[1]),
                fullMatch: match[0],
                index: match.index
            });
        }

        // If we have existing citations, enhance them with links
        if (existingCitations.length > 0) {
            // Sort by index in reverse order to avoid position shifts
            existingCitations.sort((a, b) => b.index - a.index);

            existingCitations.forEach(citation => {
                if (citation.number <= sourceItems.length) {
                    const sourceItem = sourceItems[citation.number - 1];
                    const sourceId = sourceItem.id.split('-')[1];
                    const citationLink = `<a href="#source-${sourceId}" class="citation-number" data-citation-id="${citation.number}">[${citation.number}]</a>`;

                    // Replace the plain citation with the linked version
                    content = content.substring(0, citation.index) +
                             citationLink +
                             content.substring(citation.index + citation.fullMatch.length);
                }
            });

            responseContent.innerHTML = content;
        } else {
            // Fallback: add citations to sentences if none exist
            const sentences = content.split('.');
            if (sentences.length >= sourceItems.length) {
                for (let i = 0; i < Math.min(sourceItems.length, sentences.length - 1); i++) {
                    if (sentences[i].trim().length > 0) {
                        const sourceId = sourceItems[i].id.split('-')[1];
                        sentences[i] += ` <a href="#source-${sourceId}" class="citation-number" data-citation-id="${i+1}">[${i+1}]</a>`;
                    }
                }
                responseContent.innerHTML = sentences.join('.');
            }
        }
    }

    // Setup source item interactions
    function setupSourceInteractions() {
        // Get all source items and citation numbers
        const sourceItems = document.querySelectorAll('.professional-source-card');
        const citationNumbers = document.querySelectorAll('.citation-number');

        // Add click event to source items
        sourceItems.forEach(item => {
            item.addEventListener('click', function() {
                // Get citation ID
                const citationId = this.getAttribute('data-citation-id');

                // Remove active class from all source items
                sourceItems.forEach(source => source.classList.remove('source-active'));

                // Add active class to clicked source item
                this.classList.add('source-active');

                // Highlight citation in text
                highlightCitationInText(citationId);

                // Scroll to citation in text
                scrollToCitationInText(citationId);
            });
        });

        // Add click event to citation numbers
        citationNumbers.forEach(num => {
            num.addEventListener('click', function(e) {
                e.preventDefault();

                // Get citation ID
                const citationId = this.getAttribute('data-citation-id');

                // Get source item
                const sourceElement = document.querySelector(`.professional-source-card[data-citation-id="${citationId}"]`);

                if (sourceElement) {
                    // Remove active class from all source items
                    sourceItems.forEach(source => source.classList.remove('source-active'));

                    // Add active class to corresponding source item
                    sourceElement.classList.add('source-active');

                    // Scroll to source item
                    sourceElement.scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            });
        });
    }

    // Highlight citation number in text
    function highlightCitationInText(citationId) {
        // Remove highlight from all citation numbers
        document.querySelectorAll('.citation-number').forEach(num => {
            num.classList.remove('citation-highlight');
        });

        // Add highlight to the specific citation number
        const citationNumber = document.querySelector(`.citation-number[data-citation-id="${citationId}"]`);
        if (citationNumber) {
            citationNumber.classList.add('citation-highlight');

            // Remove highlight after 2 seconds
            setTimeout(() => {
                citationNumber.classList.remove('citation-highlight');
            }, 2000);
        }
    }

    // Scroll to citation in text
    function scrollToCitationInText(citationId) {
        const citationNumber = document.querySelector(`.citation-number[data-citation-id="${citationId}"]`);
        if (citationNumber) {
            citationNumber.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }

    // Fill the follow-up question input
    function fillFollowUpQuestion(question) {
        const queryInput = document.getElementById('query');
        if (queryInput) {
            queryInput.value = question;
            queryInput.focus();
        }
    }

    // Handle form submission with loading state
    document.getElementById('follow-up-form').addEventListener('submit', function(e) {
        const submitButton = this.querySelector('button[type="submit"]');
        const buttonText = submitButton.innerHTML;

        // Show loading state
        submitButton.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> Sending...';
        submitButton.disabled = true;
    });
</script>
{% endblock %}
